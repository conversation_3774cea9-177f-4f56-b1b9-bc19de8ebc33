# Login Pages

This project now includes two login pages based on the Grade Central design:

## Available Login Pages

### 1. Standard Login Page
- **Location**: `app/login/page.tsx`
- **URL**: `http://localhost:3001/login`
- **Description**: A standard Next.js route for the login page

### 2. Auth Slot Login Page
- **Location**: `app/@auth/page.tsx`
- **URL**: Accessible as a parallel route/slot (requires layout configuration)
- **Description**: Login page in the @auth folder for parallel routing

## Features

Both login pages include:

- ✅ "Grade Central" header with dropdown icon
- ✅ "Welcome back" title
- ✅ Username input field (pre-filled with "oriD")
- ✅ Password input field
- ✅ "Forgot password?" link
- ✅ Blue "Login" button
- ✅ Responsive design
- ✅ Accessibility features (screen reader labels)
- ✅ Form validation
- ✅ Hover and focus states

## Design Details

The login pages match the provided design with:
- Clean white background
- Centered form layout
- Proper spacing and typography
- Blue accent color for the login button
- Gray color scheme for text and borders
- Rounded input fields and button

## Usage

To access the login page, start the development server:

```bash
npm run dev
```

Then navigate to:
- Standard route: `http://localhost:3001/login`
- For @auth slot: Configure parallel routing in your layout

## Customization

You can customize the login pages by:
- Modifying the form validation logic
- Adding authentication integration
- Updating the styling in the Tailwind classes
- Adding additional form fields
- Implementing forgot password functionality
