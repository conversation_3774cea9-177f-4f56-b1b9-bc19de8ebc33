# Authentication Pages

This project now includes login and forgot password pages based on the Grade Central and EduPortal designs:

## Available Pages

### 1. Standard Login Page
- **Location**: `app/login/page.tsx`
- **URL**: `http://localhost:3001/login`
- **Description**: A standard Next.js route for the login page

### 2. Auth Slot Login Page
- **Location**: `app/@auth/page.tsx`
- **URL**: Accessible as a parallel route/slot (requires layout configuration)
- **Description**: Login page in the @auth folder for parallel routing

### 3. Forgot Password Page
- **Location**: `app/forgot-password/page.tsx`
- **URL**: `http://localhost:3001/forgot-password`
- **Description**: Password reset page accessible from login pages

### 4. Auth Slot Forgot Password Page
- **Location**: `app/@auth/forgot-password/page.tsx`
- **URL**: Accessible as a parallel route/slot (requires layout configuration)
- **Description**: Forgot password page in the @auth folder for parallel routing

## Features

### Login Pages Include:
- ✅ "Grade Central" header with dropdown icon
- ✅ "Welcome back" title
- ✅ Username input field (pre-filled with "oriD")
- ✅ Password input field
- ✅ "Forgot password?" link (integrated with forgot password page)
- ✅ Blue "Login" button
- ✅ Responsive design
- ✅ Accessibility features (screen reader labels)
- ✅ Form validation
- ✅ Hover and focus states

### Forgot Password Pages Include:
- ✅ "EduPortal" header with dropdown icon
- ✅ "Forgot your password?" title
- ✅ Descriptive instructions text
- ✅ Email or Username input field
- ✅ Blue "Submit" button
- ✅ "Remember your password? Sign in" link (back to login)
- ✅ Responsive design
- ✅ Accessibility features
- ✅ Form validation
- ✅ Hover and focus states

## Design Details

The login pages match the provided design with:
- Clean white background
- Centered form layout
- Proper spacing and typography
- Blue accent color for the login button
- Gray color scheme for text and borders
- Rounded input fields and button

## Usage

To access the authentication pages, start the development server:

```bash
npm run dev
```

Then navigate to:
- **Login page**: `http://localhost:3001/login`
- **Forgot password page**: `http://localhost:3001/forgot-password`
- For @auth slots: Configure parallel routing in your layout

## Navigation Flow

1. **Login Page** → Click "Forgot password?" → **Forgot Password Page**
2. **Forgot Password Page** → Click "Remember your password? Sign in" → **Login Page**

## Customization

You can customize the login pages by:
- Modifying the form validation logic
- Adding authentication integration
- Updating the styling in the Tailwind classes
- Adding additional form fields
- Implementing forgot password functionality
