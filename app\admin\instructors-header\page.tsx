'use client';

import { useState } from 'react';
import AdminHeader from '../../../features/admin/components/layout/AdminHeader';
import InstructorsTable from '../../../features/admin/components/ui/InstructorsTable';

interface Instructor {
  id: string;
  name: string;
  courses: string[];
  email: string;
}

export default function AdminInstructorsHeaderPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [instructors] = useState<Instructor[]>([
    {
      id: '1',
      name: 'Dr. <PERSON>',
      courses: ['Calculus I', 'Linear Algebra'],
      email: '<EMAIL>'
    },
    {
      id: '2',
      name: 'Prof. <PERSON>',
      courses: ['Introduction to Programming', 'Data Structures'],
      email: '<EMAIL>'
    },
    {
      id: '3',
      name: 'Dr. <PERSON>',
      courses: ['Organic Chemistry', 'Biochemistry'],
      email: '<EMAIL>'
    },
    {
      id: '4',
      name: 'Prof<PERSON> <PERSON>',
      courses: ['World History', 'European History'],
      email: '<EMAIL>'
    },
    {
      id: '5',
      name: 'Dr. <PERSON>',
      courses: ['Creative Writing', 'American Literature'],
      email: '<EMAIL>'
    }
  ]);

  const filteredInstructors = instructors.filter(instructor =>
    instructor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    instructor.courses.some(course => 
      course.toLowerCase().includes(searchTerm.toLowerCase())
    ) ||
    instructor.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleViewDetails = (instructorId: string) => {
    console.log('View details for instructor:', instructorId);
    // TODO: Implement view details functionality
  };

  const handleAssignInstructor = () => {
    console.log('Assign instructor');
    // TODO: Implement assign instructor functionality
  };

  return (
    <div className="min-h-screen bg-white">
      <AdminHeader />
      
      <main className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Page Header */}
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Instructors</h1>
          <button
            onClick={handleAssignInstructor}
            className="bg-gray-900 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-800 transition-colors"
          >
            Assign Instructor
          </button>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Search instructors"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Instructors Table */}
        <InstructorsTable
          instructors={filteredInstructors}
          onViewDetails={handleViewDetails}
        />
      </main>
    </div>
  );
}
